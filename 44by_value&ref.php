<?php
    $arr = ["name" => "john doe","age" => 30 ,
    "profession"=> "doctor"] ;
    echo "pre>";
    print_r($arr);
    $copy = $arr;
    $alias = &$arr;
    $arr["name"] = "angella christ";
    echo $arr["name"];
    echo "<br>";
    echo $copy["name"];
    echo "<br>";
    echo $alias["name"];
    





    // pass by ref 
    function changecolour(&$color)
    {
        $newarr['colour']='red';
    }
    $object = ["shape" => "circle","size"=> "small","colour"=> "blue"];
    changecolour($object);
    print_r($object);
    

?>