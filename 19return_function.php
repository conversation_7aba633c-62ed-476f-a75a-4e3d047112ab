<?php
    // function countdown(){
    //     echo "4,3,2,1,,,";
    //     return "this is return statement";
    // }
    // // countdoen();
    // $var = countdown();
    // echo $var;





    // // more on return function
    // function new_method(){
    //     return "i want to return this method ";
    //     echo "this is new method";
    // }
    // // new_method();
    // $demo = new_method();
    // echo $demo;




    // return value
    function test()
    {
        return "67";
    }
   //   echo test();

    $var = test();
    echo $var;










// return null
function hello()
{
    return null;
}
echo hello();



?>